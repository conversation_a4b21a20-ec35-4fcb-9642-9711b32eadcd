#!/usr/bin/env python3
"""
Complete AI Trading System Launcher - NORYON V2
Launches the complete AI trading system with all components
"""

import asyncio
import subprocess
import time
import json
import logging
from datetime import datetime
import threading
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'complete_system_launch_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("CompleteSystemLauncher")


class CompleteAITradingSystemLauncher:
    """
    Complete AI Trading System Launcher
    
    Launches and coordinates:
    - Comprehensive system testing
    - Continuous trading simulation
    - Real-time monitoring dashboard
    - AI agent coordination
    """
    
    def __init__(self):
        self.processes = {}
        self.system_status = {}
        self.launch_time = None
    
    async def launch_complete_system(self):
        """Launch the complete AI trading system"""
        self.launch_time = datetime.now()
        
        logger.info("🚀 LAUNCHING COMPLETE AI TRADING SYSTEM - NORYON V2")
        logger.info("=" * 80)
        logger.info(f"🕐 Launch Time: {self.launch_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 80)
        
        try:
            # Phase 1: System Validation
            logger.info("Phase 1: System Validation")
            await self._run_system_validation()
            
            # Phase 2: Launch Trading Simulation
            logger.info("Phase 2: Launch Trading Simulation")
            await self._launch_trading_simulation()
            
            # Phase 3: Launch Monitoring Dashboard
            logger.info("Phase 3: Launch Monitoring Dashboard")
            await self._launch_monitoring_dashboard()
            
            # Phase 4: System Coordination
            logger.info("Phase 4: System Coordination")
            await self._coordinate_system_operations()
            
            # Generate launch report
            launch_report = await self._generate_launch_report()
            
            return launch_report
            
        except Exception as e:
            logger.error(f"❌ System launch error: {e}")
            return {"error": str(e)}
    
    async def _run_system_validation(self):
        """Run comprehensive system validation"""
        logger.info("🔍 Running system validation...")
        
        try:
            # Run quick system test
            result = subprocess.run([sys.executable, 'quick_system_test.py'], 
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info("✅ System validation passed")
                self.system_status['validation'] = 'PASSED'
            else:
                logger.warning("⚠️ System validation had issues")
                self.system_status['validation'] = 'WARNING'
                
        except Exception as e:
            logger.error(f"❌ System validation error: {e}")
            self.system_status['validation'] = 'FAILED'
    
    async def _launch_trading_simulation(self):
        """Launch continuous trading simulation"""
        logger.info("📊 Launching continuous trading simulation...")
        
        try:
            # Start trading simulation in background
            process = subprocess.Popen([
                sys.executable, 'continuous_trading_simulation.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.processes['trading_simulation'] = process
            
            # Wait a moment to check if it started successfully
            await asyncio.sleep(3)
            
            if process.poll() is None:  # Process is still running
                logger.info("✅ Trading simulation launched successfully")
                self.system_status['trading_simulation'] = 'RUNNING'
            else:
                logger.error("❌ Trading simulation failed to start")
                self.system_status['trading_simulation'] = 'FAILED'
                
        except Exception as e:
            logger.error(f"❌ Trading simulation launch error: {e}")
            self.system_status['trading_simulation'] = 'ERROR'
    
    async def _launch_monitoring_dashboard(self):
        """Launch real-time monitoring dashboard"""
        logger.info("🖥️ Launching monitoring dashboard...")
        
        try:
            # Wait for trading simulation to generate some data
            await asyncio.sleep(10)
            
            # Start monitoring dashboard in background
            process = subprocess.Popen([
                sys.executable, 'real_time_monitoring_dashboard.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.processes['monitoring_dashboard'] = process
            
            # Wait a moment to check if it started successfully
            await asyncio.sleep(3)
            
            if process.poll() is None:  # Process is still running
                logger.info("✅ Monitoring dashboard launched successfully")
                self.system_status['monitoring_dashboard'] = 'RUNNING'
            else:
                logger.error("❌ Monitoring dashboard failed to start")
                self.system_status['monitoring_dashboard'] = 'FAILED'
                
        except Exception as e:
            logger.error(f"❌ Monitoring dashboard launch error: {e}")
            self.system_status['monitoring_dashboard'] = 'ERROR'
    
    async def _coordinate_system_operations(self):
        """Coordinate system operations"""
        logger.info("🔄 Coordinating system operations...")
        
        try:
            # Monitor system for 5 minutes
            monitoring_duration = 300  # 5 minutes
            start_time = time.time()
            
            while time.time() - start_time < monitoring_duration:
                # Check process health
                await self._check_process_health()
                
                # Log system status
                await self._log_system_status()
                
                # Wait before next check
                await asyncio.sleep(30)  # Check every 30 seconds
            
            logger.info("✅ System coordination completed")
            self.system_status['coordination'] = 'COMPLETED'
            
        except Exception as e:
            logger.error(f"❌ System coordination error: {e}")
            self.system_status['coordination'] = 'ERROR'
    
    async def _check_process_health(self):
        """Check health of all processes"""
        try:
            for process_name, process in self.processes.items():
                if process.poll() is None:
                    # Process is still running
                    self.system_status[process_name] = 'RUNNING'
                else:
                    # Process has terminated
                    self.system_status[process_name] = 'TERMINATED'
                    logger.warning(f"⚠️ Process {process_name} has terminated")
                    
        except Exception as e:
            logger.error(f"❌ Process health check error: {e}")
    
    async def _log_system_status(self):
        """Log current system status"""
        try:
            running_processes = sum(1 for status in self.system_status.values() if status == 'RUNNING')
            total_processes = len(self.processes)
            
            uptime = time.time() - self.launch_time.timestamp()
            
            logger.info(f"📊 System Status: {running_processes}/{total_processes} processes running, "
                       f"uptime: {uptime/60:.1f} minutes")
            
        except Exception as e:
            logger.error(f"❌ System status logging error: {e}")
    
    async def _generate_launch_report(self):
        """Generate comprehensive launch report"""
        try:
            end_time = datetime.now()
            duration = (end_time - self.launch_time).total_seconds()
            
            # Check final process status
            await self._check_process_health()
            
            report = {
                'launch_summary': {
                    'launch_time': self.launch_time.isoformat(),
                    'completion_time': end_time.isoformat(),
                    'duration_seconds': duration,
                    'total_processes': len(self.processes),
                    'running_processes': sum(1 for status in self.system_status.values() if status == 'RUNNING')
                },
                'component_status': self.system_status,
                'processes': {name: 'RUNNING' if proc.poll() is None else 'TERMINATED' 
                            for name, proc in self.processes.items()},
                'system_ready': all(status in ['RUNNING', 'PASSED', 'COMPLETED'] 
                                  for status in self.system_status.values())
            }
            
            # Save report
            report_filename = f'complete_system_launch_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(report_filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            # Display final status
            logger.info("=" * 80)
            logger.info("🎯 COMPLETE AI TRADING SYSTEM LAUNCH REPORT")
            logger.info("=" * 80)
            logger.info(f"⏱️ Duration: {duration:.1f} seconds")
            logger.info(f"📊 Processes: {report['launch_summary']['running_processes']}/{report['launch_summary']['total_processes']} running")
            logger.info(f"🎯 System Ready: {'YES' if report['system_ready'] else 'NO'}")
            logger.info(f"📄 Report saved: {report_filename}")
            
            # Component status
            logger.info("\n📋 Component Status:")
            for component, status in self.system_status.items():
                status_icon = "✅" if status in ['RUNNING', 'PASSED', 'COMPLETED'] else "❌"
                logger.info(f"   {status_icon} {component}: {status}")
            
            logger.info("=" * 80)
            
            if report['system_ready']:
                logger.info("🚀 NORYON V2 AI TRADING SYSTEM IS FULLY OPERATIONAL!")
                logger.info("📊 Trading simulation is running with real AI agents")
                logger.info("🖥️ Monitoring dashboard is displaying live data")
                logger.info("🤖 AI agents are making trading decisions")
                logger.info("💰 Paper trading is executing with realistic market conditions")
            else:
                logger.info("⚠️ System launched with some issues - check component status above")
            
            logger.info("=" * 80)
            
            return report
            
        except Exception as e:
            logger.error(f"❌ Launch report generation error: {e}")
            return {"error": str(e)}
    
    def cleanup_processes(self):
        """Clean up all processes"""
        logger.info("🧹 Cleaning up processes...")
        
        for process_name, process in self.processes.items():
            try:
                if process.poll() is None:  # Process is still running
                    process.terminate()
                    logger.info(f"✅ Terminated {process_name}")
            except Exception as e:
                logger.error(f"❌ Error terminating {process_name}: {e}")


async def main():
    """Main function to launch complete system"""
    launcher = CompleteAITradingSystemLauncher()
    
    try:
        results = await launcher.launch_complete_system()
        
        # Keep system running
        logger.info("🔄 System is running. Press Ctrl+C to stop...")
        while True:
            await asyncio.sleep(60)  # Keep alive
            
    except KeyboardInterrupt:
        logger.info("🛑 System shutdown requested")
    finally:
        launcher.cleanup_processes()
        logger.info("👋 System shutdown complete")


if __name__ == "__main__":
    asyncio.run(main())
