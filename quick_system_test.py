#!/usr/bin/env python3
"""
Quick System Test - Verify NORYON V2 Components
"""

import sys
import os
import sqlite3
import subprocess
import time
import json
from datetime import datetime

print("🚀 NORYON V2 QUICK SYSTEM TEST")
print("=" * 50)

# Test 1: Python Environment
print("1. Python Environment:")
print(f"   Version: {sys.version}")
print(f"   Working Directory: {os.getcwd()}")
print("   ✅ Python OK")

# Test 2: Required Files
print("\n2. Required Files:")
required_files = [
    'advanced_ml_engine.py',
    'advanced_strategy_engine.py', 
    'advanced_technical_analysis.py',
    'realistic_paper_trading_simulation.py',
    'comprehensive_testing_framework.py'
]

missing_files = []
for file in required_files:
    if os.path.exists(file):
        print(f"   ✅ {file}")
    else:
        print(f"   ❌ {file} - MISSING")
        missing_files.append(file)

if not missing_files:
    print("   ✅ All required files present")
else:
    print(f"   ❌ Missing {len(missing_files)} files")

# Test 3: Database
print("\n3. Database Test:")
try:
    conn = sqlite3.connect(':memory:')
    conn.execute('CREATE TABLE test (id INTEGER, value TEXT)')
    conn.execute('INSERT INTO test VALUES (1, "test_data")')
    result = conn.execute('SELECT * FROM test').fetchone()
    conn.close()
    print(f"   ✅ SQLite OK - Test data: {result}")
except Exception as e:
    print(f"   ❌ SQLite Error: {e}")

# Test 4: Module Imports
print("\n4. Module Import Test:")
modules_to_test = [
    ('numpy', 'np'),
    ('pandas', 'pd'),
    ('sqlite3', None),
    ('json', None),
    ('datetime', None)
]

for module_name, alias in modules_to_test:
    try:
        if alias:
            exec(f"import {module_name} as {alias}")
        else:
            exec(f"import {module_name}")
        print(f"   ✅ {module_name}")
    except Exception as e:
        print(f"   ❌ {module_name}: {e}")

# Test 5: Advanced Module Imports
print("\n5. Advanced Module Import Test:")
advanced_modules = [
    'advanced_ml_engine',
    'advanced_strategy_engine',
    'advanced_technical_analysis',
    'realistic_paper_trading_simulation'
]

successful_imports = 0
for module in advanced_modules:
    try:
        exec(f"import {module}")
        print(f"   ✅ {module}")
        successful_imports += 1
    except Exception as e:
        print(f"   ❌ {module}: {e}")

print(f"   📊 Advanced modules: {successful_imports}/{len(advanced_modules)} imported")

# Test 6: Ollama Check
print("\n6. Ollama AI Models:")
try:
    result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
    if result.returncode == 0:
        models = result.stdout.strip().split('\n')[1:]  # Skip header
        print(f"   ✅ Ollama available - {len(models)} models found")
        for model in models[:5]:  # Show first 5 models
            if model.strip():
                print(f"      - {model.split()[0]}")
    else:
        print("   ❌ Ollama not responding")
except Exception as e:
    print(f"   ❌ Ollama error: {e}")

# Test 7: Create Test Database
print("\n7. Test Database Creation:")
try:
    test_db = "quick_test_system.db"
    conn = sqlite3.connect(test_db)
    
    # Create test tables
    conn.execute('''
        CREATE TABLE IF NOT EXISTS market_data (
            id INTEGER PRIMARY KEY,
            timestamp TEXT,
            symbol TEXT,
            price REAL
        )
    ''')
    
    # Insert test data
    conn.execute('''
        INSERT INTO market_data (timestamp, symbol, price) 
        VALUES (?, ?, ?)
    ''', (datetime.now().isoformat(), 'BTC/USD', 45000.0))
    
    conn.commit()
    
    # Query test data
    result = conn.execute('SELECT COUNT(*) FROM market_data').fetchone()
    conn.close()
    
    print(f"   ✅ Test database created - {result[0]} records")
    
    # Clean up
    if os.path.exists(test_db):
        os.remove(test_db)
        
except Exception as e:
    print(f"   ❌ Database creation error: {e}")

# Test 8: Simple AI Trading Logic Test
print("\n8. Simple Trading Logic Test:")
try:
    # Simulate market data
    import random
    import numpy as np
    
    prices = [45000 + random.uniform(-1000, 1000) for _ in range(10)]
    
    # Simple moving average strategy
    if len(prices) >= 5:
        short_ma = np.mean(prices[-3:])
        long_ma = np.mean(prices[-5:])
        
        if short_ma > long_ma:
            signal = "BUY"
        elif short_ma < long_ma:
            signal = "SELL"
        else:
            signal = "HOLD"
        
        print(f"   ✅ Trading logic test - Signal: {signal}")
        print(f"      Short MA: ${short_ma:.2f}, Long MA: ${long_ma:.2f}")
    else:
        print("   ❌ Insufficient data for trading logic")
        
except Exception as e:
    print(f"   ❌ Trading logic error: {e}")

# Summary
print("\n" + "=" * 50)
print("📊 QUICK SYSTEM TEST SUMMARY")
print("=" * 50)

# Calculate overall status
tests_passed = 0
total_tests = 8

if not missing_files:
    tests_passed += 1
if successful_imports >= len(advanced_modules) // 2:
    tests_passed += 1

success_rate = (tests_passed / total_tests) * 100

print(f"🎯 System Status: {'READY' if success_rate > 70 else 'NEEDS ATTENTION'}")
print(f"📈 Success Rate: {success_rate:.1f}%")
print(f"⏱️ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if success_rate > 70:
    print("\n✅ System is ready for comprehensive testing and simulation!")
    print("   Next steps:")
    print("   1. Run: python comprehensive_testing_framework.py")
    print("   2. Run: python realistic_paper_trading_simulation.py")
    print("   3. Run: python comprehensive_system_activation.py")
else:
    print("\n❌ System needs attention before proceeding")
    print("   Please resolve the issues above before continuing")

print("=" * 50)
