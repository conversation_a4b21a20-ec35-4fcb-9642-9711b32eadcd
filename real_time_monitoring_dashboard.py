#!/usr/bin/env python3
"""
Real-Time Monitoring Dashboard - NORYON V2
Live monitoring of AI trading simulation with real-time updates
"""

import asyncio
import sqlite3
import time
import json
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any
import threading

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("MonitoringDashboard")


class RealTimeMonitoringDashboard:
    """
    Real-Time Monitoring Dashboard
    
    Provides live monitoring of:
    - Trading performance
    - AI agent decisions
    - Portfolio status
    - Market data
    - Risk metrics
    """
    
    def __init__(self, db_path: str = "continuous_trading_simulation.db"):
        self.db_path = db_path
        self.running = False
        self.last_update = None
        
        # Dashboard data
        self.dashboard_data = {
            'portfolio': {},
            'recent_trades': [],
            'ai_decisions': [],
            'performance_metrics': {},
            'market_data': {},
            'system_status': {}
        }
    
    async def start_monitoring(self):
        """Start real-time monitoring"""
        logger.info("🖥️ STARTING REAL-TIME MONITORING DASHBOARD")
        logger.info("=" * 60)
        
        self.running = True
        
        try:
            while self.running:
                await self._update_dashboard_data()
                await self._display_dashboard()
                await asyncio.sleep(10)  # Update every 10 seconds
                
        except KeyboardInterrupt:
            logger.info("🛑 Monitoring stopped by user")
        finally:
            self.running = False
    
    async def _update_dashboard_data(self):
        """Update all dashboard data"""
        try:
            # Check if database exists
            try:
                conn = sqlite3.connect(self.db_path)
                
                # Update portfolio data
                await self._update_portfolio_data(conn)
                
                # Update recent trades
                await self._update_recent_trades(conn)
                
                # Update AI decisions
                await self._update_ai_decisions(conn)
                
                # Update performance metrics
                await self._update_performance_metrics(conn)
                
                # Update system status
                await self._update_system_status()
                
                conn.close()
                self.last_update = datetime.now()
                
            except sqlite3.OperationalError:
                # Database doesn't exist yet
                self.dashboard_data['system_status']['database_status'] = 'WAITING_FOR_DATA'
                
        except Exception as e:
            logger.error(f"❌ Error updating dashboard data: {e}")
    
    async def _update_portfolio_data(self, conn):
        """Update portfolio data"""
        try:
            # Get latest portfolio snapshot
            cursor = conn.execute('''
                SELECT total_value, cash_balance, positions_value, total_pnl, daily_pnl
                FROM performance_snapshots 
                ORDER BY timestamp DESC 
                LIMIT 1
            ''')
            
            row = cursor.fetchone()
            if row:
                self.dashboard_data['portfolio'] = {
                    'total_value': row[0],
                    'cash_balance': row[1],
                    'positions_value': row[2],
                    'total_pnl': row[3],
                    'daily_pnl': row[4],
                    'return_percentage': (row[3] / 100000) * 100  # Assuming 100k initial
                }
            else:
                self.dashboard_data['portfolio'] = {
                    'total_value': 100000,
                    'cash_balance': 100000,
                    'positions_value': 0,
                    'total_pnl': 0,
                    'daily_pnl': 0,
                    'return_percentage': 0
                }
                
        except Exception as e:
            logger.error(f"❌ Error updating portfolio data: {e}")
    
    async def _update_recent_trades(self, conn):
        """Update recent trades"""
        try:
            cursor = conn.execute('''
                SELECT timestamp, symbol, side, quantity, entry_price, pnl, strategy
                FROM trade_execution_log 
                ORDER BY timestamp DESC 
                LIMIT 10
            ''')
            
            trades = []
            for row in cursor.fetchall():
                trades.append({
                    'timestamp': row[0],
                    'symbol': row[1],
                    'side': row[2],
                    'quantity': row[3],
                    'price': row[4],
                    'pnl': row[5],
                    'strategy': row[6]
                })
            
            self.dashboard_data['recent_trades'] = trades
            
        except Exception as e:
            logger.error(f"❌ Error updating recent trades: {e}")
    
    async def _update_ai_decisions(self, conn):
        """Update AI decisions"""
        try:
            cursor = conn.execute('''
                SELECT timestamp, symbol, agent_name, decision, confidence, reasoning, market_price, executed
                FROM ai_decision_log 
                ORDER BY timestamp DESC 
                LIMIT 10
            ''')
            
            decisions = []
            for row in cursor.fetchall():
                decisions.append({
                    'timestamp': row[0],
                    'symbol': row[1],
                    'agent': row[2],
                    'decision': row[3],
                    'confidence': row[4],
                    'reasoning': row[5],
                    'price': row[6],
                    'executed': bool(row[7])
                })
            
            self.dashboard_data['ai_decisions'] = decisions
            
        except Exception as e:
            logger.error(f"❌ Error updating AI decisions: {e}")
    
    async def _update_performance_metrics(self, conn):
        """Update performance metrics"""
        try:
            # Get latest performance data
            cursor = conn.execute('''
                SELECT total_trades, win_rate, max_drawdown
                FROM performance_snapshots 
                ORDER BY timestamp DESC 
                LIMIT 1
            ''')
            
            row = cursor.fetchone()
            if row:
                # Calculate additional metrics
                cursor_trades = conn.execute('SELECT COUNT(*) FROM trade_execution_log')
                total_trades = cursor_trades.fetchone()[0]
                
                cursor_wins = conn.execute('SELECT COUNT(*) FROM trade_execution_log WHERE pnl > 0')
                winning_trades = cursor_wins.fetchone()[0]
                
                cursor_losses = conn.execute('SELECT COUNT(*) FROM trade_execution_log WHERE pnl < 0')
                losing_trades = cursor_losses.fetchone()[0]
                
                self.dashboard_data['performance_metrics'] = {
                    'total_trades': total_trades,
                    'winning_trades': winning_trades,
                    'losing_trades': losing_trades,
                    'win_rate': (winning_trades / total_trades * 100) if total_trades > 0 else 0,
                    'max_drawdown': row[2] if row[2] else 0
                }
            else:
                self.dashboard_data['performance_metrics'] = {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'win_rate': 0,
                    'max_drawdown': 0
                }
                
        except Exception as e:
            logger.error(f"❌ Error updating performance metrics: {e}")
    
    async def _update_system_status(self):
        """Update system status"""
        try:
            self.dashboard_data['system_status'] = {
                'status': 'RUNNING' if self.running else 'STOPPED',
                'last_update': self.last_update.isoformat() if self.last_update else None,
                'uptime_seconds': time.time() - (self.last_update.timestamp() if self.last_update else time.time()),
                'database_status': 'CONNECTED'
            }
            
        except Exception as e:
            logger.error(f"❌ Error updating system status: {e}")
    
    async def _display_dashboard(self):
        """Display the dashboard"""
        try:
            # Clear screen (works on most terminals)
            print("\033[2J\033[H")
            
            # Dashboard header
            print("🚀 NORYON V2 - REAL-TIME TRADING DASHBOARD")
            print("=" * 80)
            print(f"⏰ Last Update: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"🔄 Status: {self.dashboard_data['system_status'].get('status', 'UNKNOWN')}")
            print("=" * 80)
            
            # Portfolio section
            portfolio = self.dashboard_data['portfolio']
            print("\n💰 PORTFOLIO STATUS")
            print("-" * 40)
            print(f"Total Value:     ${portfolio.get('total_value', 0):>12,.2f}")
            print(f"Cash Balance:    ${portfolio.get('cash_balance', 0):>12,.2f}")
            print(f"Positions Value: ${portfolio.get('positions_value', 0):>12,.2f}")
            print(f"Total P&L:       ${portfolio.get('total_pnl', 0):>12,.2f}")
            print(f"Return:          {portfolio.get('return_percentage', 0):>12.2f}%")
            
            # Performance metrics
            metrics = self.dashboard_data['performance_metrics']
            print("\n📊 PERFORMANCE METRICS")
            print("-" * 40)
            print(f"Total Trades:    {metrics.get('total_trades', 0):>12}")
            print(f"Winning Trades:  {metrics.get('winning_trades', 0):>12}")
            print(f"Losing Trades:   {metrics.get('losing_trades', 0):>12}")
            print(f"Win Rate:        {metrics.get('win_rate', 0):>12.1f}%")
            print(f"Max Drawdown:    {metrics.get('max_drawdown', 0):>12.2f}%")
            
            # Recent trades
            print("\n📈 RECENT TRADES")
            print("-" * 80)
            print(f"{'Time':<12} {'Symbol':<8} {'Side':<4} {'Quantity':<10} {'Price':<10} {'P&L':<10}")
            print("-" * 80)
            
            for trade in self.dashboard_data['recent_trades'][:5]:
                timestamp = datetime.fromisoformat(trade['timestamp']).strftime('%H:%M:%S')
                pnl_color = "📈" if trade['pnl'] > 0 else "📉" if trade['pnl'] < 0 else "➡️"
                print(f"{timestamp:<12} {trade['symbol']:<8} {trade['side']:<4} {trade['quantity']:<10.4f} "
                      f"${trade['price']:<9.2f} {pnl_color}${trade['pnl']:<9.2f}")
            
            # Recent AI decisions
            print("\n🤖 RECENT AI DECISIONS")
            print("-" * 80)
            print(f"{'Time':<12} {'Symbol':<8} {'Decision':<8} {'Confidence':<10} {'Executed':<8}")
            print("-" * 80)
            
            for decision in self.dashboard_data['ai_decisions'][:5]:
                timestamp = datetime.fromisoformat(decision['timestamp']).strftime('%H:%M:%S')
                executed = "✅" if decision['executed'] else "❌"
                confidence_bar = "█" * int(decision['confidence'] * 10)
                print(f"{timestamp:<12} {decision['symbol']:<8} {decision['decision']:<8} "
                      f"{decision['confidence']:<10.2f} {executed:<8}")
            
            print("\n" + "=" * 80)
            print("Press Ctrl+C to stop monitoring")
            print("=" * 80)
            
        except Exception as e:
            logger.error(f"❌ Error displaying dashboard: {e}")
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.running = False


async def main():
    """Main function to run monitoring dashboard"""
    dashboard = RealTimeMonitoringDashboard()
    await dashboard.start_monitoring()


if __name__ == "__main__":
    asyncio.run(main())
