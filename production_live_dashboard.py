#!/usr/bin/env python3
"""
Production Live Dashboard - NORYON V2
Real-time dashboard showing actual AI trading system performance
"""

import asyncio
import sqlite3
import time
import json
from datetime import datetime, timedelta
import logging
import os
import subprocess
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ProductionDashboard")


class ProductionLiveDashboard:
    """
    Production Live Dashboard
    
    Shows real-time data from the production AI trading system:
    - Live market data
    - AI agent decisions
    - Trade executions
    - Portfolio performance
    - System health
    """
    
    def __init__(self, db_path: str = "production_ai_trading.db"):
        self.db_path = db_path
        self.running = False
        self.last_update = None
        
        # Dashboard data
        self.dashboard_data = {
            'market_data': {},
            'ai_decisions': [],
            'recent_trades': [],
            'portfolio': {},
            'system_metrics': {},
            'agent_status': {}
        }
        
    async def start_live_dashboard(self):
        """Start the live dashboard"""
        logger.info("🖥️ STARTING PRODUCTION LIVE DASHBOARD")
        logger.info("=" * 80)
        
        self.running = True
        
        try:
            while self.running:
                # Update all dashboard data
                await self._update_dashboard_data()
                
                # Display the dashboard
                await self._display_live_dashboard()
                
                # Wait 5 seconds before next update
                await asyncio.sleep(5)
                
        except KeyboardInterrupt:
            logger.info("🛑 Dashboard stopped by user")
        finally:
            self.running = False
            
    async def _update_dashboard_data(self):
        """Update all dashboard data from database"""
        try:
            if not os.path.exists(self.db_path):
                self.dashboard_data['system_status'] = 'WAITING_FOR_DATA'
                return
                
            conn = sqlite3.connect(self.db_path)
            
            # Update market data
            await self._update_market_data(conn)
            
            # Update AI decisions
            await self._update_ai_decisions(conn)
            
            # Update recent trades
            await self._update_recent_trades(conn)
            
            # Update portfolio data
            await self._update_portfolio_data(conn)
            
            # Update system metrics
            await self._update_system_metrics(conn)
            
            # Check AI agent status
            await self._check_ai_agent_status()
            
            conn.close()
            self.last_update = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ Error updating dashboard data: {e}")
            
    async def _update_market_data(self, conn):
        """Update latest market data"""
        try:
            cursor = conn.execute('''
                SELECT symbol, price, change_24h, volume, timestamp
                FROM market_data 
                WHERE timestamp > datetime('now', '-1 minute')
                ORDER BY timestamp DESC
            ''')
            
            market_data = {}
            for row in cursor.fetchall():
                symbol = row[0]
                if symbol not in market_data:  # Get latest for each symbol
                    market_data[symbol] = {
                        'price': row[1],
                        'change_24h': row[2],
                        'volume': row[3],
                        'timestamp': row[4]
                    }
                    
            self.dashboard_data['market_data'] = market_data
            
        except Exception as e:
            logger.error(f"Error updating market data: {e}")
            
    async def _update_ai_decisions(self, conn):
        """Update recent AI decisions"""
        try:
            cursor = conn.execute('''
                SELECT timestamp, symbol, action, confidence, agreement, agent_count, reasoning
                FROM team_consensus 
                ORDER BY timestamp DESC 
                LIMIT 10
            ''')
            
            decisions = []
            for row in cursor.fetchall():
                decisions.append({
                    'timestamp': row[0],
                    'symbol': row[1],
                    'action': row[2],
                    'confidence': row[3],
                    'agreement': row[4],
                    'agent_count': row[5],
                    'reasoning': row[6][:50] + '...' if len(row[6]) > 50 else row[6]
                })
                
            self.dashboard_data['ai_decisions'] = decisions
            
        except Exception as e:
            logger.error(f"Error updating AI decisions: {e}")
            
    async def _update_recent_trades(self, conn):
        """Update recent trade executions"""
        try:
            cursor = conn.execute('''
                SELECT timestamp, symbol, action, quantity, price, value, pnl, status
                FROM trade_executions 
                ORDER BY timestamp DESC 
                LIMIT 10
            ''')
            
            trades = []
            for row in cursor.fetchall():
                trades.append({
                    'timestamp': row[0],
                    'symbol': row[1],
                    'action': row[2],
                    'quantity': row[3],
                    'price': row[4],
                    'value': row[5],
                    'pnl': row[6] if row[6] else 0,
                    'status': row[7]
                })
                
            self.dashboard_data['recent_trades'] = trades
            
        except Exception as e:
            logger.error(f"Error updating recent trades: {e}")
            
    async def _update_portfolio_data(self, conn):
        """Update portfolio performance data"""
        try:
            cursor = conn.execute('''
                SELECT total_value, cash_balance, positions_value, total_pnl, total_trades, win_rate, active_positions
                FROM portfolio_snapshots 
                ORDER BY timestamp DESC 
                LIMIT 1
            ''')
            
            row = cursor.fetchone()
            if row:
                self.dashboard_data['portfolio'] = {
                    'total_value': row[0],
                    'cash_balance': row[1],
                    'positions_value': row[2],
                    'total_pnl': row[3],
                    'total_trades': row[4],
                    'win_rate': row[5],
                    'active_positions': row[6],
                    'return_percentage': (row[3] / 100000) * 100  # Assuming 100k initial
                }
            else:
                self.dashboard_data['portfolio'] = {
                    'total_value': 100000,
                    'cash_balance': 100000,
                    'positions_value': 0,
                    'total_pnl': 0,
                    'total_trades': 0,
                    'win_rate': 0,
                    'active_positions': 0,
                    'return_percentage': 0
                }
                
        except Exception as e:
            logger.error(f"Error updating portfolio data: {e}")
            
    async def _update_system_metrics(self, conn):
        """Update system performance metrics"""
        try:
            cursor = conn.execute('''
                SELECT uptime_seconds, total_cycles, successful_cycles, ai_response_rate, avg_cycle_time, system_health
                FROM system_metrics 
                ORDER BY timestamp DESC 
                LIMIT 1
            ''')
            
            row = cursor.fetchone()
            if row:
                self.dashboard_data['system_metrics'] = {
                    'uptime_seconds': row[0],
                    'total_cycles': row[1],
                    'successful_cycles': row[2],
                    'ai_response_rate': row[3],
                    'avg_cycle_time': row[4],
                    'system_health': row[5],
                    'success_rate': (row[2] / row[1] * 100) if row[1] > 0 else 0
                }
            else:
                self.dashboard_data['system_metrics'] = {
                    'uptime_seconds': 0,
                    'total_cycles': 0,
                    'successful_cycles': 0,
                    'ai_response_rate': 0,
                    'avg_cycle_time': 0,
                    'system_health': 'STARTING',
                    'success_rate': 0
                }
                
        except Exception as e:
            logger.error(f"Error updating system metrics: {e}")
            
    async def _check_ai_agent_status(self):
        """Check AI agent availability"""
        try:
            # Check Ollama models
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                models = result.stdout.strip().split('\n')[1:]  # Skip header
                available_models = [model.split()[0] for model in models if model.strip()]
                
                # Check specific models used by our agents
                agent_models = ['marco-o1:7b', 'cogito:32b', 'command-r:35b', 'gemma3:27b', 'mistral-small:24b', 'qwen3:32b']
                
                agent_status = {}
                for model in agent_models:
                    agent_status[model] = 'AVAILABLE' if any(model.split(':')[0] in available for available in available_models) else 'UNAVAILABLE'
                    
                self.dashboard_data['agent_status'] = agent_status
            else:
                self.dashboard_data['agent_status'] = {'ollama': 'UNAVAILABLE'}
                
        except Exception as e:
            self.dashboard_data['agent_status'] = {'error': str(e)}
            
    async def _display_live_dashboard(self):
        """Display the live dashboard"""
        try:
            # Clear screen
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # Dashboard header
            print("🚀 NORYON V2 - PRODUCTION AI TRADING SYSTEM LIVE DASHBOARD")
            print("=" * 100)
            print(f"⏰ Last Update: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # System status
            metrics = self.dashboard_data['system_metrics']
            health_icon = "💚" if metrics.get('system_health') == 'HEALTHY' else "🔴" if metrics.get('system_health') == 'ERROR' else "🟡"
            print(f"{health_icon} System Health: {metrics.get('system_health', 'UNKNOWN')}")
            print(f"⏱️ Uptime: {metrics.get('uptime_seconds', 0)/60:.1f} minutes")
            print(f"🔄 Cycles: {metrics.get('total_cycles', 0)} ({metrics.get('success_rate', 0):.1f}% success)")
            print("=" * 100)
            
            # Portfolio section
            portfolio = self.dashboard_data['portfolio']
            print("\n💰 PORTFOLIO PERFORMANCE")
            print("-" * 50)
            print(f"Total Value:     ${portfolio.get('total_value', 0):>15,.2f}")
            print(f"Cash Balance:    ${portfolio.get('cash_balance', 0):>15,.2f}")
            print(f"Positions Value: ${portfolio.get('positions_value', 0):>15,.2f}")
            print(f"Total P&L:       ${portfolio.get('total_pnl', 0):>15,.2f}")
            print(f"Return:          {portfolio.get('return_percentage', 0):>15.2f}%")
            print(f"Total Trades:    {portfolio.get('total_trades', 0):>15}")
            print(f"Win Rate:        {portfolio.get('win_rate', 0):>15.1f}%")
            print(f"Active Positions:{portfolio.get('active_positions', 0):>15}")
            
            # Market data section
            print("\n📊 LIVE MARKET DATA")
            print("-" * 80)
            print(f"{'Symbol':<12} {'Price':<12} {'24h Change':<12} {'Volume':<15} {'Status'}")
            print("-" * 80)
            
            market_data = self.dashboard_data['market_data']
            for symbol, data in list(market_data.items())[:8]:  # Show top 8
                change_icon = "📈" if data['change_24h'] > 0 else "📉" if data['change_24h'] < 0 else "➡️"
                print(f"{symbol:<12} ${data['price']:<11.2f} {change_icon}{data['change_24h']:<10.2f}% {data['volume']:<14,.0f} LIVE")
                
            # AI decisions section
            print("\n🤖 RECENT AI TEAM DECISIONS")
            print("-" * 100)
            print(f"{'Time':<10} {'Symbol':<10} {'Action':<6} {'Confidence':<10} {'Agreement':<10} {'Agents':<7} {'Reasoning'}")
            print("-" * 100)
            
            for decision in self.dashboard_data['ai_decisions'][:5]:
                timestamp = datetime.fromisoformat(decision['timestamp']).strftime('%H:%M:%S')
                action_icon = "🟢" if decision['action'] == 'BUY' else "🔴" if decision['action'] == 'SELL' else "🟡"
                print(f"{timestamp:<10} {decision['symbol']:<10} {action_icon}{decision['action']:<5} "
                      f"{decision['confidence']:<10.2f} {decision['agreement']:<10.2f} "
                      f"{decision['agent_count']:<7} {decision['reasoning'][:30]}")
                      
            # Recent trades section
            print("\n📈 RECENT TRADE EXECUTIONS")
            print("-" * 90)
            print(f"{'Time':<10} {'Symbol':<10} {'Action':<6} {'Quantity':<12} {'Price':<10} {'P&L':<10} {'Status'}")
            print("-" * 90)
            
            for trade in self.dashboard_data['recent_trades'][:5]:
                timestamp = datetime.fromisoformat(trade['timestamp']).strftime('%H:%M:%S')
                action_icon = "🟢" if trade['action'] == 'BUY' else "🔴" if trade['action'] == 'SELL' else "🟡"
                status_icon = "✅" if trade['status'] == 'EXECUTED' else "❌"
                pnl_str = f"${trade['pnl']:+.2f}" if trade['pnl'] != 0 else "-"
                
                print(f"{timestamp:<10} {trade['symbol']:<10} {action_icon}{trade['action']:<5} "
                      f"{trade['quantity']:<12.4f} ${trade['price']:<9.2f} {pnl_str:<10} {status_icon}{trade['status']}")
                      
            # AI agent status
            print("\n🤖 AI AGENT STATUS")
            print("-" * 50)
            agent_status = self.dashboard_data['agent_status']
            for model, status in agent_status.items():
                status_icon = "✅" if status == 'AVAILABLE' else "❌"
                print(f"{status_icon} {model:<20} {status}")
                
            # System metrics
            print("\n📊 SYSTEM PERFORMANCE METRICS")
            print("-" * 50)
            print(f"AI Response Rate:    {metrics.get('ai_response_rate', 0)*100:>10.1f}%")
            print(f"Avg Cycle Time:      {metrics.get('avg_cycle_time', 0):>10.2f}s")
            print(f"Successful Cycles:   {metrics.get('successful_cycles', 0):>10}")
            print(f"Total Cycles:        {metrics.get('total_cycles', 0):>10}")
            
            print("\n" + "=" * 100)
            print("🔄 Dashboard updates every 5 seconds | Press Ctrl+C to stop")
            print("=" * 100)
            
        except Exception as e:
            logger.error(f"❌ Error displaying dashboard: {e}")
            
    def stop_dashboard(self):
        """Stop the dashboard"""
        self.running = False


async def main():
    """Main function to run the live dashboard"""
    dashboard = ProductionLiveDashboard()
    await dashboard.start_live_dashboard()


if __name__ == "__main__":
    asyncio.run(main())
