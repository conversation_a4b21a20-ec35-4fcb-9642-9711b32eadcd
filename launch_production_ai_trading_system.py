#!/usr/bin/env python3
"""
Launch Production AI Trading System - NORYON V2
Launches the complete production-grade AI trading system exactly like real life
"""

import asyncio
import subprocess
import time
import json
import logging
import threading
import sys
import os
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'production_system_launch_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ProductionSystemLauncher")


class ProductionSystemLauncher:
    """
    Production System Launcher
    
    Launches and coordinates the complete production AI trading system:
    - Production AI trading engine
    - Live dashboard
    - System monitoring
    - Error recovery
    """
    
    def __init__(self):
        self.processes = {}
        self.system_status = {}
        self.launch_time = None
        self.running = False
        
    async def launch_production_system(self):
        """Launch the complete production AI trading system"""
        self.launch_time = datetime.now()
        
        logger.info("🚀 LAUNCHING PRODUCTION AI TRADING SYSTEM - NORYON V2")
        logger.info("=" * 100)
        logger.info(f"🕐 Launch Time: {self.launch_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("🎯 Target: Complete production-grade AI trading system")
        logger.info("💰 Initial Capital: $100,000")
        logger.info("🤖 AI Agents: 6 specialized trading agents")
        logger.info("📊 Trading Pairs: 10 cryptocurrency pairs")
        logger.info("⚡ Real-time: Live market simulation with AI decisions")
        logger.info("=" * 100)
        
        try:
            # Phase 1: System Prerequisites Check
            logger.info("Phase 1: System Prerequisites Check")
            await self._check_system_prerequisites()
            
            # Phase 2: Launch Production Trading Engine
            logger.info("Phase 2: Launch Production Trading Engine")
            await self._launch_production_trading_engine()
            
            # Phase 3: Launch Live Dashboard
            logger.info("Phase 3: Launch Live Dashboard")
            await self._launch_live_dashboard()
            
            # Phase 4: System Monitoring and Coordination
            logger.info("Phase 4: System Monitoring and Coordination")
            await self._monitor_production_system()
            
        except Exception as e:
            logger.error(f"❌ Production system launch error: {e}")
            await self._emergency_shutdown()
            
    async def _check_system_prerequisites(self):
        """Check all system prerequisites"""
        logger.info("🔍 Checking system prerequisites...")
        
        # Check Python version
        python_version = sys.version_info
        if python_version.major >= 3 and python_version.minor >= 8:
            logger.info(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro} - OK")
            self.system_status['python'] = 'OK'
        else:
            logger.error(f"❌ Python version {python_version.major}.{python_version.minor} too old")
            self.system_status['python'] = 'ERROR'
            
        # Check required files
        required_files = [
            'production_grade_ai_trading_system.py',
            'production_live_dashboard.py'
        ]
        
        missing_files = []
        for file in required_files:
            if os.path.exists(file):
                logger.info(f"✅ {file} - Found")
            else:
                logger.error(f"❌ {file} - Missing")
                missing_files.append(file)
                
        if not missing_files:
            self.system_status['files'] = 'OK'
        else:
            self.system_status['files'] = f'MISSING: {len(missing_files)} files'
            
        # Check Ollama availability
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                models = result.stdout.strip().split('\n')[1:]  # Skip header
                model_count = len([m for m in models if m.strip()])
                logger.info(f"✅ Ollama available - {model_count} models found")
                self.system_status['ollama'] = f'OK - {model_count} models'
                
                # Check specific models
                required_models = ['marco-o1', 'cogito', 'command-r', 'gemma3', 'mistral-small', 'qwen3']
                available_models = []
                for model in required_models:
                    if any(model in line for line in models):
                        available_models.append(model)
                        
                logger.info(f"✅ AI Models: {len(available_models)}/{len(required_models)} available")
                self.system_status['ai_models'] = f'{len(available_models)}/{len(required_models)} available'
            else:
                logger.error("❌ Ollama not responding")
                self.system_status['ollama'] = 'ERROR'
        except Exception as e:
            logger.error(f"❌ Ollama check failed: {e}")
            self.system_status['ollama'] = 'ERROR'
            
        # Check dependencies
        try:
            import numpy, pandas, sqlite3, asyncio
            logger.info("✅ Required dependencies available")
            self.system_status['dependencies'] = 'OK'
        except ImportError as e:
            logger.error(f"❌ Missing dependencies: {e}")
            self.system_status['dependencies'] = 'ERROR'
            
        # Summary
        errors = sum(1 for status in self.system_status.values() if 'ERROR' in str(status))
        if errors == 0:
            logger.info("✅ All prerequisites met - System ready for launch")
        else:
            logger.warning(f"⚠️ {errors} prerequisite issues found")
            
    async def _launch_production_trading_engine(self):
        """Launch the production trading engine"""
        logger.info("🚀 Launching production AI trading engine...")
        
        try:
            # Start the production trading system
            process = subprocess.Popen([
                sys.executable, 'production_grade_ai_trading_system.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes['trading_engine'] = process
            
            # Wait a moment to check if it started successfully
            await asyncio.sleep(5)
            
            if process.poll() is None:  # Process is still running
                logger.info("✅ Production trading engine launched successfully")
                self.system_status['trading_engine'] = 'RUNNING'
                
                # Give it time to initialize
                logger.info("⏳ Waiting for trading engine initialization...")
                await asyncio.sleep(15)
                
                # Check if database is being created
                if os.path.exists('production_ai_trading.db'):
                    logger.info("✅ Trading database created - System is operational")
                else:
                    logger.info("⏳ Trading database not yet created - Still initializing")
                    
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ Trading engine failed to start")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                self.system_status['trading_engine'] = 'FAILED'
                
        except Exception as e:
            logger.error(f"❌ Trading engine launch error: {e}")
            self.system_status['trading_engine'] = 'ERROR'
            
    async def _launch_live_dashboard(self):
        """Launch the live dashboard"""
        logger.info("🖥️ Launching live dashboard...")
        
        try:
            # Wait for trading engine to generate some data
            logger.info("⏳ Waiting for trading data generation...")
            await asyncio.sleep(20)
            
            # Start the live dashboard
            process = subprocess.Popen([
                sys.executable, 'production_live_dashboard.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes['live_dashboard'] = process
            
            # Wait a moment to check if it started successfully
            await asyncio.sleep(3)
            
            if process.poll() is None:  # Process is still running
                logger.info("✅ Live dashboard launched successfully")
                self.system_status['live_dashboard'] = 'RUNNING'
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ Live dashboard failed to start")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                self.system_status['live_dashboard'] = 'FAILED'
                
        except Exception as e:
            logger.error(f"❌ Live dashboard launch error: {e}")
            self.system_status['live_dashboard'] = 'ERROR'
            
    async def _monitor_production_system(self):
        """Monitor the production system"""
        logger.info("📊 Starting production system monitoring...")
        
        self.running = True
        monitoring_duration = 1800  # 30 minutes
        start_time = time.time()
        
        logger.info("=" * 100)
        logger.info("🎯 PRODUCTION AI TRADING SYSTEM IS NOW OPERATIONAL!")
        logger.info("=" * 100)
        logger.info("📊 Trading Engine: Processing market data and making AI decisions")
        logger.info("🖥️ Live Dashboard: Displaying real-time performance metrics")
        logger.info("🤖 AI Agents: Analyzing markets and executing trades")
        logger.info("💰 Portfolio: Starting with $100,000 virtual capital")
        logger.info("⏰ Monitoring Duration: 30 minutes")
        logger.info("=" * 100)
        
        try:
            while self.running and (time.time() - start_time) < monitoring_duration:
                # Check process health
                await self._check_process_health()
                
                # Log system status
                await self._log_system_status()
                
                # Check for database updates
                await self._check_database_activity()
                
                # Wait before next check
                await asyncio.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            logger.info("🛑 System monitoring stopped by user")
        finally:
            await self._shutdown_system()
            
    async def _check_process_health(self):
        """Check health of all processes"""
        try:
            for process_name, process in self.processes.items():
                if process.poll() is None:
                    # Process is still running
                    self.system_status[process_name] = 'RUNNING'
                else:
                    # Process has terminated
                    self.system_status[process_name] = 'TERMINATED'
                    logger.error(f"❌ Process {process_name} has terminated unexpectedly")
                    
                    # Try to get error output
                    try:
                        stdout, stderr = process.communicate(timeout=1)
                        if stderr:
                            logger.error(f"Process {process_name} error: {stderr}")
                    except:
                        pass
                        
        except Exception as e:
            logger.error(f"❌ Process health check error: {e}")
            
    async def _log_system_status(self):
        """Log current system status"""
        try:
            uptime = time.time() - self.launch_time.timestamp()
            running_processes = sum(1 for status in self.system_status.values() if status == 'RUNNING')
            total_processes = len(self.processes)
            
            logger.info(f"📊 System Status: {running_processes}/{total_processes} processes running, "
                       f"uptime: {uptime/60:.1f} minutes")
                       
            # Check specific component status
            for component, status in self.system_status.items():
                if component in ['trading_engine', 'live_dashboard']:
                    status_icon = "✅" if status == 'RUNNING' else "❌"
                    logger.info(f"   {status_icon} {component}: {status}")
                    
        except Exception as e:
            logger.error(f"❌ System status logging error: {e}")
            
    async def _check_database_activity(self):
        """Check database activity to verify system is working"""
        try:
            if os.path.exists('production_ai_trading.db'):
                import sqlite3
                conn = sqlite3.connect('production_ai_trading.db')
                
                # Check recent market data
                cursor = conn.execute("SELECT COUNT(*) FROM market_data WHERE timestamp > datetime('now', '-5 minutes')")
                recent_market_data = cursor.fetchone()[0]
                
                # Check recent AI decisions
                cursor = conn.execute("SELECT COUNT(*) FROM team_consensus WHERE timestamp > datetime('now', '-5 minutes')")
                recent_decisions = cursor.fetchone()[0]
                
                # Check recent trades
                cursor = conn.execute("SELECT COUNT(*) FROM trade_executions WHERE timestamp > datetime('now', '-5 minutes')")
                recent_trades = cursor.fetchone()[0]
                
                conn.close()
                
                if recent_market_data > 0 or recent_decisions > 0:
                    logger.info(f"💓 System Activity: {recent_market_data} market updates, "
                               f"{recent_decisions} AI decisions, {recent_trades} trades (last 5 min)")
                else:
                    logger.warning("⚠️ No recent database activity detected")
                    
        except Exception as e:
            logger.error(f"❌ Database activity check error: {e}")
            
    async def _shutdown_system(self):
        """Shutdown the system gracefully"""
        logger.info("🛑 Shutting down production AI trading system...")
        
        self.running = False
        
        # Terminate all processes
        for process_name, process in self.processes.items():
            try:
                if process.poll() is None:  # Process is still running
                    logger.info(f"⏹️ Terminating {process_name}...")
                    process.terminate()
                    
                    # Wait for graceful shutdown
                    try:
                        process.wait(timeout=10)
                        logger.info(f"✅ {process_name} terminated gracefully")
                    except subprocess.TimeoutExpired:
                        logger.warning(f"⚠️ Force killing {process_name}...")
                        process.kill()
                        
            except Exception as e:
                logger.error(f"❌ Error terminating {process_name}: {e}")
                
        # Generate final report
        await self._generate_final_launch_report()
        
        logger.info("=" * 100)
        logger.info("🏁 PRODUCTION AI TRADING SYSTEM SHUTDOWN COMPLETE")
        logger.info("=" * 100)
        
    async def _generate_final_launch_report(self):
        """Generate final launch report"""
        try:
            end_time = datetime.now()
            duration = (end_time - self.launch_time).total_seconds()
            
            # Check final system status
            await self._check_process_health()
            
            report = {
                'launch_summary': {
                    'launch_time': self.launch_time.isoformat(),
                    'shutdown_time': end_time.isoformat(),
                    'total_duration_minutes': duration / 60,
                    'system_components': len(self.processes),
                    'final_status': self.system_status
                },
                'system_performance': {
                    'trading_engine_status': self.system_status.get('trading_engine', 'UNKNOWN'),
                    'dashboard_status': self.system_status.get('live_dashboard', 'UNKNOWN'),
                    'ollama_status': self.system_status.get('ollama', 'UNKNOWN'),
                    'ai_models_status': self.system_status.get('ai_models', 'UNKNOWN')
                }
            }
            
            # Save report
            report_filename = f'production_launch_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(report_filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
                
            logger.info(f"📄 Final launch report saved: {report_filename}")
            logger.info(f"⏱️ Total Runtime: {duration/60:.1f} minutes")
            
            # Check if we have trading data
            if os.path.exists('production_ai_trading.db'):
                logger.info("✅ Trading database created with real data")
                logger.info("📊 System successfully processed market data and AI decisions")
            else:
                logger.warning("⚠️ No trading database found")
                
        except Exception as e:
            logger.error(f"❌ Final report generation error: {e}")
            
    async def _emergency_shutdown(self):
        """Emergency shutdown in case of critical errors"""
        logger.error("🚨 EMERGENCY SHUTDOWN INITIATED")
        
        for process_name, process in self.processes.items():
            try:
                if process.poll() is None:
                    process.kill()
                    logger.info(f"🛑 Emergency killed {process_name}")
            except:
                pass


async def main():
    """Main function to launch the production system"""
    launcher = ProductionSystemLauncher()
    
    try:
        await launcher.launch_production_system()
    except KeyboardInterrupt:
        logger.info("🛑 Production system launch interrupted by user")
    except Exception as e:
        logger.error(f"❌ Production system launch failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
