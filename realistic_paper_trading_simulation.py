#!/usr/bin/env python3
"""
Realistic Paper Trading Simulation System
Exactly like real-life trading with AI teams, real market conditions, and live operations
"""

import asyncio
import sqlite3
import subprocess
import time
import json
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import logging
import threading
from dataclasses import dataclass
from typing import Dict, List, Any, Optional
import random

# Import real trading components
from advanced_ml_engine import AdvancedMLEngine
from advanced_strategy_engine import AdvancedStrategyEngine
from advanced_technical_analysis import AdvancedTechnicalAnalysis

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("PaperTradingSimulation")


@dataclass
class MarketTick:
    """Real-time market tick data"""
    symbol: str
    timestamp: datetime
    bid: float
    ask: float
    last: float
    volume: int
    high_24h: float
    low_24h: float
    change_24h: float


@dataclass
class AIAgent:
    """AI Agent for trading decisions"""
    name: str
    model: str
    role: str
    confidence_threshold: float
    last_decision: Optional[str] = None
    performance_score: float = 0.5
    active: bool = True


@dataclass
class Position:
    """Trading position"""
    symbol: str
    side: str  # LONG/SHORT
    quantity: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    realized_pnl: float
    entry_time: datetime
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None


class RealisticMarketSimulator:
    """Simulates realistic market conditions with volatility, spreads, slippage"""
    
    def __init__(self):
        self.symbols = ['BTC/USD', 'ETH/USD', 'ADA/USD', 'SOL/USD', 'AAPL', 'TSLA', 'MSFT', 'GOOGL', 'NVDA', 'AMZN']
        self.base_prices = {
            'BTC/USD': 45000, 'ETH/USD': 2800, 'ADA/USD': 0.45, 'SOL/USD': 95, 'AAPL': 180,
            'TSLA': 250, 'MSFT': 380, 'GOOGL': 140, 'NVDA': 800, 'AMZN': 155
        }
        self.current_prices = self.base_prices.copy()
        self.price_history = {symbol: [] for symbol in self.symbols}
        self.running = False
        
    def start_market_simulation(self):
        """Start realistic market price simulation"""
        self.running = True
        threading.Thread(target=self._simulate_market_data, daemon=True).start()
        logger.info("🔄 Market simulation started")
    
    def stop_market_simulation(self):
        """Stop market simulation"""
        self.running = False
        logger.info("⏹️ Market simulation stopped")
    
    def _simulate_market_data(self):
        """Simulate realistic market data with volatility"""
        while self.running:
            for symbol in self.symbols:
                # Realistic price movement
                volatility = 0.001 if 'USD' in symbol else 0.002  # Crypto more volatile
                price_change = np.random.normal(0, volatility)
                
                # Add market hours effect (higher volatility during trading hours)
                current_hour = datetime.now().hour
                if 9 <= current_hour <= 16:  # Market hours
                    price_change *= 1.5
                
                # Update price
                self.current_prices[symbol] *= (1 + price_change)
                
                # Store price history
                self.price_history[symbol].append({
                    'timestamp': datetime.now(),
                    'price': self.current_prices[symbol],
                    'volume': random.randint(100000, 2000000)
                })
                
                # Keep only last 1000 points
                if len(self.price_history[symbol]) > 1000:
                    self.price_history[symbol] = self.price_history[symbol][-1000:]
            
            time.sleep(1)  # Update every second
    
    def get_market_tick(self, symbol: str) -> MarketTick:
        """Get realistic market tick with bid/ask spread"""
        if symbol not in self.current_prices:
            return None
        
        last_price = self.current_prices[symbol]
        spread_pct = 0.001 if 'USD' in symbol else 0.002  # Realistic spreads
        
        spread = last_price * spread_pct
        bid = last_price - spread/2
        ask = last_price + spread/2
        
        # Calculate 24h stats
        history = self.price_history[symbol]
        if len(history) >= 24:
            prices_24h = [h['price'] for h in history[-24:]]
            high_24h = max(prices_24h)
            low_24h = min(prices_24h)
            change_24h = (last_price - prices_24h[0]) / prices_24h[0]
        else:
            high_24h = last_price * 1.02
            low_24h = last_price * 0.98
            change_24h = random.uniform(-0.05, 0.05)
        
        return MarketTick(
            symbol=symbol,
            timestamp=datetime.now(),
            bid=bid,
            ask=ask,
            last=last_price,
            volume=random.randint(100000, 2000000),
            high_24h=high_24h,
            low_24h=low_24h,
            change_24h=change_24h
        )


class AITradingTeam:
    """AI Trading Team with multiple specialized agents"""
    
    def __init__(self):
        self.agents = [
            AIAgent("MarketAnalyst", "marco-o1:7b", "market_analysis", 0.7),
            AIAgent("TechnicalAnalyst", "cogito:32b", "technical_analysis", 0.75),
            AIAgent("RiskManager", "command-r:35b", "risk_management", 0.8),
            AIAgent("NewsAnalyst", "gemma3:27b", "sentiment_analysis", 0.7),
            AIAgent("QuantTrader", "mistral-small:24b", "quantitative_trading", 0.75),
            AIAgent("PortfolioManager", "qwen3:32b", "portfolio_management", 0.8),
        ]
        self.team_decisions = []
        self.consensus_threshold = 0.6
    
    async def get_team_decision(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get consensus decision from AI trading team"""
        decisions = []
        
        for agent in self.agents:
            if not agent.active:
                continue
                
            try:
                # Simulate AI agent decision making
                decision = await self._get_agent_decision(agent, market_data)
                if decision:
                    decisions.append(decision)
                    agent.last_decision = decision['action']
                    
            except Exception as e:
                logger.error(f"Agent {agent.name} error: {e}")
                agent.active = False
        
        # Calculate team consensus
        consensus = self._calculate_consensus(decisions)
        
        self.team_decisions.append({
            'timestamp': datetime.now(),
            'decisions': decisions,
            'consensus': consensus
        })
        
        return consensus
    
    async def _get_agent_decision(self, agent: AIAgent, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get decision from individual AI agent"""
        symbol = market_data.get('symbol', 'BTC/USD')
        price = market_data.get('price', 45000)
        
        # Create agent-specific prompt
        if agent.role == "market_analysis":
            prompt = f"Analyze {symbol} at ${price:.2f}. Market trend analysis. Respond: BUY/SELL/HOLD with confidence 0-1."
        elif agent.role == "technical_analysis":
            rsi = market_data.get('rsi_14', 50)
            prompt = f"Technical analysis {symbol}: Price ${price:.2f}, RSI {rsi:.1f}. Signal: BUY/SELL/HOLD with confidence."
        elif agent.role == "risk_management":
            prompt = f"Risk assessment {symbol} at ${price:.2f}. Position sizing recommendation. BUY/SELL/HOLD with risk score."
        else:
            prompt = f"Trading decision for {symbol} at ${price:.2f}. Your role: {agent.role}. Decision: BUY/SELL/HOLD."
        
        try:
            # Try to get real AI response
            result = subprocess.run(
                ['ollama', 'run', agent.model, prompt],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0:
                response = result.stdout.strip().upper()
                
                # Parse AI response
                action = "HOLD"
                confidence = 0.5
                
                if "BUY" in response:
                    action = "BUY"
                    confidence = 0.7 + random.uniform(0, 0.2)
                elif "SELL" in response:
                    action = "SELL"
                    confidence = 0.7 + random.uniform(0, 0.2)
                
                return {
                    'agent': agent.name,
                    'action': action,
                    'confidence': confidence,
                    'reasoning': response[:100],
                    'timestamp': datetime.now()
                }
            else:
                # Fallback to simulated decision
                return self._simulate_agent_decision(agent, market_data)
                
        except Exception as e:
            logger.warning(f"AI agent {agent.name} fallback to simulation: {e}")
            return self._simulate_agent_decision(agent, market_data)
    
    def _simulate_agent_decision(self, agent: AIAgent, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate agent decision when AI is not available"""
        # Use market data to make realistic decisions
        rsi = market_data.get('rsi_14', 50)
        price_change = market_data.get('momentum_5d', 0)
        
        action = "HOLD"
        confidence = 0.5
        
        if agent.role == "technical_analysis":
            if rsi < 30:
                action = "BUY"
                confidence = 0.8
            elif rsi > 70:
                action = "SELL"
                confidence = 0.8
        elif agent.role == "market_analysis":
            if price_change > 0.02:
                action = "BUY"
                confidence = 0.7
            elif price_change < -0.02:
                action = "SELL"
                confidence = 0.7
        
        return {
            'agent': agent.name,
            'action': action,
            'confidence': confidence,
            'reasoning': f"Simulated {agent.role} decision",
            'timestamp': datetime.now()
        }
    
    def _calculate_consensus(self, decisions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate team consensus from individual decisions"""
        if not decisions:
            return {'action': 'HOLD', 'confidence': 0.0, 'agreement': 0.0}
        
        # Weight decisions by confidence
        buy_weight = sum(d['confidence'] for d in decisions if d['action'] == 'BUY')
        sell_weight = sum(d['confidence'] for d in decisions if d['action'] == 'SELL')
        hold_weight = sum(d['confidence'] for d in decisions if d['action'] == 'HOLD')
        
        total_weight = buy_weight + sell_weight + hold_weight
        
        if total_weight == 0:
            return {'action': 'HOLD', 'confidence': 0.0, 'agreement': 0.0}
        
        # Determine consensus action
        if buy_weight > sell_weight and buy_weight > hold_weight:
            action = 'BUY'
            confidence = buy_weight / total_weight
        elif sell_weight > buy_weight and sell_weight > hold_weight:
            action = 'SELL'
            confidence = sell_weight / total_weight
        else:
            action = 'HOLD'
            confidence = hold_weight / total_weight
        
        # Calculate agreement level
        max_weight = max(buy_weight, sell_weight, hold_weight)
        agreement = max_weight / total_weight
        
        return {
            'action': action,
            'confidence': confidence,
            'agreement': agreement,
            'vote_distribution': {
                'BUY': buy_weight / total_weight,
                'SELL': sell_weight / total_weight,
                'HOLD': hold_weight / total_weight
            },
            'participating_agents': len(decisions)
        }


class RealisticPaperTradingSystem:
    """Complete realistic paper trading system"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}
        self.order_history = []
        self.pnl_history = []
        
        # Initialize components
        self.market_simulator = RealisticMarketSimulator()
        self.ai_team = AITradingTeam()
        self.ml_engine = AdvancedMLEngine()
        self.strategy_engine = AdvancedStrategyEngine()
        self.technical_analyzer = AdvancedTechnicalAnalysis()
        
        # Setup database
        self.db_path = "realistic_paper_trading.db"
        self._setup_database()
        
        # Trading parameters
        self.max_position_size = 0.1  # 10% max per position
        self.max_daily_loss = 0.02    # 2% max daily loss
        self.slippage_factor = 0.001  # 0.1% slippage
        
        self.running = False
        
    def _setup_database(self):
        """Setup comprehensive trading database"""
        conn = sqlite3.connect(self.db_path)
        
        # Market data
        conn.execute('''
            CREATE TABLE IF NOT EXISTS market_ticks (
                id INTEGER PRIMARY KEY,
                timestamp TEXT,
                symbol TEXT,
                bid REAL,
                ask REAL,
                last REAL,
                volume INTEGER,
                high_24h REAL,
                low_24h REAL,
                change_24h REAL
            )
        ''')
        
        # AI decisions
        conn.execute('''
            CREATE TABLE IF NOT EXISTS ai_decisions (
                id INTEGER PRIMARY KEY,
                timestamp TEXT,
                symbol TEXT,
                agent_name TEXT,
                action TEXT,
                confidence REAL,
                reasoning TEXT
            )
        ''')
        
        # Orders
        conn.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY,
                timestamp TEXT,
                symbol TEXT,
                side TEXT,
                quantity REAL,
                price REAL,
                filled_price REAL,
                status TEXT,
                slippage REAL,
                commission REAL
            )
        ''')
        
        # Positions
        conn.execute('''
            CREATE TABLE IF NOT EXISTS positions (
                id INTEGER PRIMARY KEY,
                timestamp TEXT,
                symbol TEXT,
                side TEXT,
                quantity REAL,
                entry_price REAL,
                current_price REAL,
                unrealized_pnl REAL,
                stop_loss REAL,
                take_profit REAL
            )
        ''')
        
        # Portfolio snapshots
        conn.execute('''
            CREATE TABLE IF NOT EXISTS portfolio_snapshots (
                id INTEGER PRIMARY KEY,
                timestamp TEXT,
                total_value REAL,
                cash REAL,
                positions_value REAL,
                daily_pnl REAL,
                total_pnl REAL,
                drawdown REAL
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("✅ Database setup complete")
    
    async def start_trading_simulation(self):
        """Start the complete trading simulation"""
        logger.info("🚀 Starting realistic paper trading simulation...")
        
        self.running = True
        
        # Start market simulation
        self.market_simulator.start_market_simulation()
        
        # Train ML models
        await self._train_ml_models()
        
        # Start trading loop
        await self._main_trading_loop()
    
    def stop_trading_simulation(self):
        """Stop trading simulation"""
        self.running = False
        self.market_simulator.stop_market_simulation()
        logger.info("⏹️ Trading simulation stopped")
    
    async def _train_ml_models(self):
        """Train ML models with historical data"""
        logger.info("🧠 Training ML models...")
        
        # Generate training data
        training_data = []
        for symbol in self.market_simulator.symbols:
            for i in range(100):
                price = self.market_simulator.base_prices[symbol] * (1 + np.random.normal(0, 0.1))
                training_data.append({
                    'price': price,
                    'price_history': [price * (1 + np.random.normal(0, 0.01)) for _ in range(50)],
                    'symbol': symbol,
                    'rsi': random.uniform(20, 80),
                    'volume': random.randint(100000, 2000000)
                })
        
        results = self.ml_engine.train_models(training_data)
        logger.info(f"✅ ML models trained: {len(results)} models")
    
    async def _main_trading_loop(self):
        """Main trading loop - exactly like real trading"""
        logger.info("🔄 Starting main trading loop...")
        
        while self.running:
            try:
                # Process each symbol
                for symbol in self.market_simulator.symbols:
                    await self._process_symbol(symbol)
                
                # Update portfolio
                await self._update_portfolio()
                
                # Risk management
                await self._risk_management_check()
                
                # Wait before next cycle (simulate real trading frequency)
                await asyncio.sleep(5)  # 5 second cycles
                
            except Exception as e:
                logger.error(f"Trading loop error: {e}")
                await asyncio.sleep(1)
    
    async def _process_symbol(self, symbol: str):
        """Process trading decisions for a symbol"""
        try:
            # Get market data
            market_tick = self.market_simulator.get_market_tick(symbol)
            if not market_tick:
                return
            
            # Store market tick
            self._store_market_tick(market_tick)
            
            # Get technical analysis
            price_history = [h['price'] for h in self.market_simulator.price_history[symbol][-50:]]
            if len(price_history) < 20:
                return
            
            technical_data = self.technical_analyzer.calculate_all_indicators(price_history)
            
            # Prepare market data for analysis
            market_data = {
                'symbol': symbol,
                'price': market_tick.last,
                'bid': market_tick.bid,
                'ask': market_tick.ask,
                'volume': market_tick.volume,
                'change_24h': market_tick.change_24h,
                'price_history': price_history,
                **technical_data
            }
            
            # Get AI team decision
            ai_consensus = await self.ai_team.get_team_decision(market_data)
            
            # Get strategy signals
            strategy_signals = self.strategy_engine.generate_signals(market_data)
            
            # Get ML predictions
            ml_predictions = self.ml_engine.predict_signals(market_data)
            
            # Make trading decision
            await self._make_trading_decision(symbol, market_tick, ai_consensus, strategy_signals, ml_predictions)
            
        except Exception as e:
            logger.error(f"Error processing {symbol}: {e}")
    
    async def _make_trading_decision(self, symbol: str, market_tick: MarketTick, 
                                   ai_consensus: Dict, strategy_signals: List, ml_predictions: Dict):
        """Make final trading decision combining all inputs"""
        
        # Combine all signals
        total_buy_confidence = 0
        total_sell_confidence = 0
        
        # AI team weight (40%)
        if ai_consensus['action'] == 'BUY':
            total_buy_confidence += ai_consensus['confidence'] * 0.4
        elif ai_consensus['action'] == 'SELL':
            total_sell_confidence += ai_consensus['confidence'] * 0.4
        
        # Strategy signals weight (30%)
        for signal in strategy_signals:
            if signal.direction == 'BUY':
                total_buy_confidence += signal.confidence * 0.3
            elif signal.direction == 'SELL':
                total_sell_confidence += signal.confidence * 0.3
        
        # ML predictions weight (30%)
        ml_signal = ml_predictions.get('ensemble_signal', {})
        if ml_signal.get('signal') == 'BUY':
            total_buy_confidence += ml_signal.get('confidence', 0) * 0.3
        elif ml_signal.get('signal') == 'SELL':
            total_sell_confidence += ml_signal.get('confidence', 0) * 0.3
        
        # Execute trades based on combined confidence
        if total_buy_confidence > 0.7 and symbol not in self.positions:
            await self._execute_buy_order(symbol, market_tick, total_buy_confidence)
        elif total_sell_confidence > 0.7 and symbol in self.positions:
            await self._execute_sell_order(symbol, market_tick, total_sell_confidence)
    
    async def _execute_buy_order(self, symbol: str, market_tick: MarketTick, confidence: float):
        """Execute realistic buy order with slippage and commissions"""
        try:
            # Calculate position size based on confidence and risk
            risk_amount = self.current_capital * self.max_position_size * confidence
            
            # Apply slippage (buy at ask + slippage)
            execution_price = market_tick.ask * (1 + self.slippage_factor)
            quantity = risk_amount / execution_price
            
            # Commission (0.1%)
            commission = risk_amount * 0.001
            total_cost = risk_amount + commission
            
            if total_cost <= self.current_capital:
                # Execute order
                self.current_capital -= total_cost
                
                position = Position(
                    symbol=symbol,
                    side='LONG',
                    quantity=quantity,
                    entry_price=execution_price,
                    current_price=market_tick.last,
                    unrealized_pnl=0,
                    realized_pnl=0,
                    entry_time=datetime.now(),
                    stop_loss=execution_price * 0.95,  # 5% stop loss
                    take_profit=execution_price * 1.10  # 10% take profit
                )
                
                self.positions[symbol] = position
                
                # Store order
                self._store_order(symbol, 'BUY', quantity, market_tick.ask, execution_price, 
                                'FILLED', self.slippage_factor, commission)
                
                logger.info(f"✅ BUY {quantity:.4f} {symbol} at ${execution_price:.2f} (confidence: {confidence:.2f})")
                
        except Exception as e:
            logger.error(f"Buy order error for {symbol}: {e}")
    
    async def _execute_sell_order(self, symbol: str, market_tick: MarketTick, confidence: float):
        """Execute realistic sell order"""
        try:
            if symbol not in self.positions:
                return
            
            position = self.positions[symbol]
            
            # Apply slippage (sell at bid - slippage)
            execution_price = market_tick.bid * (1 - self.slippage_factor)
            
            # Commission
            proceeds = position.quantity * execution_price
            commission = proceeds * 0.001
            net_proceeds = proceeds - commission
            
            # Calculate P&L
            cost_basis = position.quantity * position.entry_price
            realized_pnl = net_proceeds - cost_basis
            
            # Execute order
            self.current_capital += net_proceeds
            
            # Store order
            self._store_order(symbol, 'SELL', position.quantity, market_tick.bid, 
                            execution_price, 'FILLED', self.slippage_factor, commission)
            
            # Remove position
            del self.positions[symbol]
            
            logger.info(f"✅ SELL {position.quantity:.4f} {symbol} at ${execution_price:.2f} (P&L: ${realized_pnl:.2f})")
            
        except Exception as e:
            logger.error(f"Sell order error for {symbol}: {e}")
    
    def _store_market_tick(self, tick: MarketTick):
        """Store market tick in database"""
        conn = sqlite3.connect(self.db_path)
        conn.execute('''
            INSERT INTO market_ticks (timestamp, symbol, bid, ask, last, volume, high_24h, low_24h, change_24h)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (tick.timestamp.isoformat(), tick.symbol, tick.bid, tick.ask, tick.last, 
              tick.volume, tick.high_24h, tick.low_24h, tick.change_24h))
        conn.commit()
        conn.close()
    
    def _store_order(self, symbol: str, side: str, quantity: float, order_price: float, 
                    filled_price: float, status: str, slippage: float, commission: float):
        """Store order in database"""
        conn = sqlite3.connect(self.db_path)
        conn.execute('''
            INSERT INTO orders (timestamp, symbol, side, quantity, price, filled_price, status, slippage, commission)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (datetime.now().isoformat(), symbol, side, quantity, order_price, 
              filled_price, status, slippage, commission))
        conn.commit()
        conn.close()
    
    async def _update_portfolio(self):
        """Update portfolio values and P&L"""
        try:
            positions_value = 0
            total_unrealized_pnl = 0
            
            for symbol, position in self.positions.items():
                market_tick = self.market_simulator.get_market_tick(symbol)
                if market_tick:
                    position.current_price = market_tick.last
                    position_value = position.quantity * market_tick.last
                    position.unrealized_pnl = position_value - (position.quantity * position.entry_price)
                    
                    positions_value += position_value
                    total_unrealized_pnl += position.unrealized_pnl
            
            total_portfolio_value = self.current_capital + positions_value
            total_pnl = total_portfolio_value - self.initial_capital
            daily_pnl = total_unrealized_pnl  # Simplified
            
            # Store portfolio snapshot
            conn = sqlite3.connect(self.db_path)
            conn.execute('''
                INSERT INTO portfolio_snapshots (timestamp, total_value, cash, positions_value, daily_pnl, total_pnl, drawdown)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (datetime.now().isoformat(), total_portfolio_value, self.current_capital, 
                  positions_value, daily_pnl, total_pnl, 0))
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Portfolio update error: {e}")
    
    async def _risk_management_check(self):
        """Perform risk management checks"""
        try:
            # Check stop losses and take profits
            for symbol, position in list(self.positions.items()):
                market_tick = self.market_simulator.get_market_tick(symbol)
                if not market_tick:
                    continue
                
                current_price = market_tick.last
                
                # Check stop loss
                if position.stop_loss and current_price <= position.stop_loss:
                    logger.warning(f"🛑 Stop loss triggered for {symbol} at ${current_price:.2f}")
                    await self._execute_sell_order(symbol, market_tick, 1.0)
                
                # Check take profit
                elif position.take_profit and current_price >= position.take_profit:
                    logger.info(f"🎯 Take profit triggered for {symbol} at ${current_price:.2f}")
                    await self._execute_sell_order(symbol, market_tick, 1.0)
            
        except Exception as e:
            logger.error(f"Risk management error: {e}")


async def main():
    """Main function to run the realistic paper trading simulation"""
    print("🚀 REALISTIC PAPER TRADING SIMULATION")
    print("=" * 60)
    print("Exactly like real-life trading with AI teams and live market conditions")
    print()
    
    # Initialize system
    trading_system = RealisticPaperTradingSystem(initial_capital=100000)
    
    try:
        # Start simulation
        await trading_system.start_trading_simulation()
        
    except KeyboardInterrupt:
        print("\n⏹️ Stopping simulation...")
        trading_system.stop_trading_simulation()
        
        # Print final results
        print(f"\n📊 FINAL RESULTS:")
        print(f"Initial Capital: ${trading_system.initial_capital:,.2f}")
        print(f"Current Capital: ${trading_system.current_capital:,.2f}")
        print(f"Active Positions: {len(trading_system.positions)}")
        print(f"Database: {trading_system.db_path}")


if __name__ == "__main__":
    asyncio.run(main())
