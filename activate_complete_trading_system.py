#!/usr/bin/env python3
"""
Complete Trading System Activation
Activates the full realistic paper trading system with AI teams
"""

import asyncio
import subprocess
import time
import json
import logging
from datetime import datetime
from pathlib import Path

# Import the complete system
from realistic_paper_trading_simulation import RealisticPaperTradingSystem

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("TradingSystemActivation")


class CompleteTradingSystemActivation:
    """Complete trading system activation and monitoring"""
    
    def __init__(self):
        self.start_time = time.time()
        self.trading_system = None
        self.activation_results = {}
        
    async def activate_complete_system(self):
        """Activate the complete trading system"""
        logger.info("🚀 ACTIVATING COMPLETE TRADING SYSTEM")
        logger.info("=" * 60)
        
        try:
            # Phase 1: System Verification
            logger.info("Phase 1: System Verification")
            await self._verify_system_components()
            
            # Phase 2: AI Model Testing
            logger.info("Phase 2: AI Model Testing")
            await self._test_ai_models()
            
            # Phase 3: Trading System Initialization
            logger.info("Phase 3: Trading System Initialization")
            await self._initialize_trading_system()
            
            # Phase 4: Market Simulation Activation
            logger.info("Phase 4: Market Simulation Activation")
            await self._activate_market_simulation()
            
            # Phase 5: AI Team Activation
            logger.info("Phase 5: AI Team Activation")
            await self._activate_ai_teams()
            
            # Phase 6: Live Trading Simulation
            logger.info("Phase 6: Live Trading Simulation")
            await self._start_live_simulation()
            
            return self.activation_results
            
        except Exception as e:
            logger.error(f"System activation error: {e}")
            return {"error": str(e)}
    
    async def _verify_system_components(self):
        """Verify all system components are available"""
        logger.info("Verifying system components...")
        
        verification_results = {}
        
        # Check Python files
        required_files = [
            'advanced_ml_engine.py',
            'advanced_strategy_engine.py', 
            'advanced_technical_analysis.py',
            'realistic_paper_trading_simulation.py'
        ]
        
        missing_files = []
        for file in required_files:
            if not Path(file).exists():
                missing_files.append(file)
        
        verification_results['required_files'] = {
            'total': len(required_files),
            'found': len(required_files) - len(missing_files),
            'missing': missing_files
        }
        
        # Test imports
        try:
            from advanced_ml_engine import AdvancedMLEngine
            from advanced_strategy_engine import AdvancedStrategyEngine
            from advanced_technical_analysis import AdvancedTechnicalAnalysis
            verification_results['imports'] = 'success'
            logger.info("✅ All components imported successfully")
        except Exception as e:
            verification_results['imports'] = f'error: {str(e)}'
            logger.error(f"❌ Import error: {e}")
        
        self.activation_results['system_verification'] = verification_results
    
    async def _test_ai_models(self):
        """Test AI model availability and responsiveness"""
        logger.info("Testing AI models...")
        
        ai_test_results = {}
        
        # Test Ollama availability
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                models = result.stdout.strip().split('\n')[1:]
                ai_test_results['ollama_available'] = True
                ai_test_results['total_models'] = len(models)
                logger.info(f"✅ Ollama available with {len(models)} models")
                
                # Test specific models
                test_models = ['marco-o1:7b', 'cogito:32b', 'command-r:35b']
                responsive_models = []
                
                for model in test_models:
                    try:
                        test_result = subprocess.run(
                            ['ollama', 'run', model, 'Respond with OK if operational'],
                            capture_output=True, text=True, timeout=15
                        )
                        if test_result.returncode == 0:
                            responsive_models.append(model)
                            logger.info(f"✅ {model}: Responsive")
                        else:
                            logger.warning(f"⚠️ {model}: Not responding")
                    except Exception as e:
                        logger.warning(f"⚠️ {model}: Error - {str(e)[:50]}")
                
                ai_test_results['responsive_models'] = responsive_models
                ai_test_results['response_rate'] = len(responsive_models) / len(test_models)
                
            else:
                ai_test_results['ollama_available'] = False
                logger.warning("⚠️ Ollama not available")
                
        except Exception as e:
            ai_test_results['error'] = str(e)
            logger.error(f"❌ AI model test error: {e}")
        
        self.activation_results['ai_models'] = ai_test_results
    
    async def _initialize_trading_system(self):
        """Initialize the complete trading system"""
        logger.info("Initializing trading system...")
        
        try:
            self.trading_system = RealisticPaperTradingSystem(initial_capital=100000)
            
            initialization_results = {
                'initial_capital': 100000,
                'symbols_monitored': len(self.trading_system.market_simulator.symbols),
                'ai_agents': len(self.trading_system.ai_team.agents),
                'database_path': self.trading_system.db_path,
                'status': 'initialized'
            }
            
            logger.info(f"✅ Trading system initialized:")
            logger.info(f"   Capital: ${initialization_results['initial_capital']:,}")
            logger.info(f"   Symbols: {initialization_results['symbols_monitored']}")
            logger.info(f"   AI Agents: {initialization_results['ai_agents']}")
            
            self.activation_results['trading_system'] = initialization_results
            
        except Exception as e:
            logger.error(f"❌ Trading system initialization error: {e}")
            self.activation_results['trading_system'] = {'error': str(e)}
    
    async def _activate_market_simulation(self):
        """Activate realistic market simulation"""
        logger.info("Activating market simulation...")
        
        try:
            # Start market simulation
            self.trading_system.market_simulator.start_market_simulation()
            
            # Wait for initial data
            await asyncio.sleep(3)
            
            # Verify market data
            market_results = {}
            active_symbols = 0
            
            for symbol in self.trading_system.market_simulator.symbols:
                tick = self.trading_system.market_simulator.get_market_tick(symbol)
                if tick:
                    active_symbols += 1
                    logger.info(f"📊 {symbol}: ${tick.last:.2f} (24h: {tick.change_24h:+.2%})")
            
            market_results = {
                'total_symbols': len(self.trading_system.market_simulator.symbols),
                'active_symbols': active_symbols,
                'simulation_status': 'running',
                'update_frequency': '1 second'
            }
            
            logger.info(f"✅ Market simulation active: {active_symbols} symbols streaming")
            self.activation_results['market_simulation'] = market_results
            
        except Exception as e:
            logger.error(f"❌ Market simulation error: {e}")
            self.activation_results['market_simulation'] = {'error': str(e)}
    
    async def _activate_ai_teams(self):
        """Activate AI trading teams"""
        logger.info("Activating AI trading teams...")
        
        try:
            ai_team_results = {}
            
            # Test AI team with sample data
            sample_market_data = {
                'symbol': 'BTC/USD',
                'price': 45000,
                'rsi_14': 55,
                'momentum_5d': 0.02,
                'volume': 1500000
            }
            
            # Get team decision
            team_decision = await self.trading_system.ai_team.get_team_decision(sample_market_data)
            
            ai_team_results = {
                'total_agents': len(self.trading_system.ai_team.agents),
                'active_agents': len([a for a in self.trading_system.ai_team.agents if a.active]),
                'sample_decision': team_decision,
                'consensus_threshold': self.trading_system.ai_team.consensus_threshold
            }
            
            logger.info(f"✅ AI team activated:")
            logger.info(f"   Active Agents: {ai_team_results['active_agents']}/{ai_team_results['total_agents']}")
            logger.info(f"   Sample Decision: {team_decision['action']} (confidence: {team_decision['confidence']:.2f})")
            
            self.activation_results['ai_teams'] = ai_team_results
            
        except Exception as e:
            logger.error(f"❌ AI team activation error: {e}")
            self.activation_results['ai_teams'] = {'error': str(e)}
    
    async def _start_live_simulation(self):
        """Start live trading simulation"""
        logger.info("Starting live trading simulation...")
        
        try:
            # Start the trading simulation in background
            simulation_task = asyncio.create_task(self.trading_system.start_trading_simulation())
            
            # Let it run for a short demonstration period
            logger.info("🔄 Live simulation running...")
            logger.info("   Monitoring for 30 seconds...")
            
            # Monitor for 30 seconds
            for i in range(6):
                await asyncio.sleep(5)
                
                # Check portfolio status
                portfolio_value = self.trading_system.current_capital
                active_positions = len(self.trading_system.positions)
                
                logger.info(f"   [{i*5+5}s] Portfolio: ${portfolio_value:,.2f}, Positions: {active_positions}")
            
            # Stop simulation
            self.trading_system.stop_trading_simulation()
            simulation_task.cancel()
            
            # Get final results
            simulation_results = {
                'duration_seconds': 30,
                'initial_capital': 100000,
                'final_capital': self.trading_system.current_capital,
                'active_positions': len(self.trading_system.positions),
                'total_orders': len(self.trading_system.order_history),
                'pnl': self.trading_system.current_capital - 100000,
                'status': 'completed'
            }
            
            logger.info(f"✅ Live simulation completed:")
            logger.info(f"   Final Capital: ${simulation_results['final_capital']:,.2f}")
            logger.info(f"   P&L: ${simulation_results['pnl']:+,.2f}")
            logger.info(f"   Positions: {simulation_results['active_positions']}")
            
            self.activation_results['live_simulation'] = simulation_results
            
        except Exception as e:
            logger.error(f"❌ Live simulation error: {e}")
            self.activation_results['live_simulation'] = {'error': str(e)}
    
    def generate_activation_report(self):
        """Generate comprehensive activation report"""
        end_time = time.time()
        duration = end_time - self.start_time
        
        report = {
            'activation_summary': {
                'start_time': datetime.fromtimestamp(self.start_time).isoformat(),
                'end_time': datetime.fromtimestamp(end_time).isoformat(),
                'duration_seconds': duration,
                'status': 'SUCCESS' if 'error' not in str(self.activation_results) else 'PARTIAL'
            },
            'activation_results': self.activation_results
        }
        
        # Save report
        report_file = f'complete_trading_system_activation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📄 Activation report saved: {report_file}")
        return report


async def main():
    """Main activation function"""
    print("🚀 COMPLETE TRADING SYSTEM ACTIVATION")
    print("=" * 60)
    print("Activating realistic paper trading with AI teams and live simulation")
    print()
    
    activation_system = CompleteTradingSystemActivation()
    
    try:
        # Run activation
        results = await activation_system.activate_complete_system()
        
        # Generate report
        final_report = activation_system.generate_activation_report()
        
        print("\n" + "=" * 60)
        print("COMPLETE TRADING SYSTEM ACTIVATION RESULTS")
        print("=" * 60)
        
        # Display summary
        if 'system_verification' in results:
            sys_ver = results['system_verification']
            print(f"System Components: {sys_ver.get('required_files', {}).get('found', 0)}/4 files found")
        
        if 'ai_models' in results:
            ai_models = results['ai_models']
            print(f"AI Models: {ai_models.get('total_models', 0)} available, {len(ai_models.get('responsive_models', []))} responsive")
        
        if 'trading_system' in results:
            trading = results['trading_system']
            print(f"Trading System: ${trading.get('initial_capital', 0):,} capital, {trading.get('symbols_monitored', 0)} symbols")
        
        if 'market_simulation' in results:
            market = results['market_simulation']
            print(f"Market Simulation: {market.get('active_symbols', 0)} symbols streaming")
        
        if 'ai_teams' in results:
            ai_team = results['ai_teams']
            print(f"AI Teams: {ai_team.get('active_agents', 0)} agents active")
        
        if 'live_simulation' in results:
            live_sim = results['live_simulation']
            print(f"Live Simulation: ${live_sim.get('pnl', 0):+,.2f} P&L, {live_sim.get('active_positions', 0)} positions")
        
        print(f"\nStatus: {final_report['activation_summary']['status']}")
        print(f"Duration: {final_report['activation_summary']['duration_seconds']:.1f} seconds")
        print(f"Report: {final_report.get('report_file', 'Generated')}")
        
        print("\n✅ COMPLETE TRADING SYSTEM FULLY ACTIVATED!")
        
    except KeyboardInterrupt:
        print("\n⏹️ Activation interrupted by user")
    except Exception as e:
        print(f"\n❌ Activation failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
